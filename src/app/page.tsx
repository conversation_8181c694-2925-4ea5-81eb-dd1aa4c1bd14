'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import Image from 'next/image';

interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface MatchResult {
  filename: string;
  label?: string;
  confidence: number;
  distance: number;
  faceIndex: number;
  boundingBox: BoundingBox;
  referenceFaceIndex?: number;
  referenceTotalFaces?: number;
  referenceBoundingBox?: BoundingBox;
}

interface DetectedFace {
  faceIndex: number;
  boundingBox: BoundingBox;
  matchCount: number;
  bestMatch: MatchResult | null;
}

interface RecognitionResult {
  success: boolean;
  totalReferenceImages: number;
  detectedFaces?: DetectedFace[];
  detectedFaceCount?: number;
  matches: MatchResult[];
  matchCount: number;
  confidenceThreshold?: number;
  error?: string;
  errorType?: 'NETWORK' | 'NO_FACE' | 'NO_REFERENCE' | 'PROCESSING' | 'TOO_MANY_FACES' | 'UNKNOWN';
}

export default function Home() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<RecognitionResult | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [continuousMode, setContinuousMode] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Simple camera function based on your reference code
  const startCameraSimple = useCallback(async () => {
    try {
      setError(null);
      console.log('🎥 Starting camera with simple approach...');

      // Simple getUserMedia call like your reference
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      console.log('✅ Camera stream obtained:', stream);

      if (videoRef.current) {
        // Clean up existing stream (from your reference cleanup)
        if (videoRef.current.srcObject) {
          const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
          tracks.forEach((track: MediaStreamTrack) => track.stop());
        }

        // Set stream to video element (your reference approach)
        videoRef.current.srcObject = stream;

        // Set camera state
        setIsCameraOpen(true);
        console.log('🎯 Camera state set to OPEN');
      }
    } catch (error: any) {
      console.error("Camera access denied or error: ", error);

      let errorMessage = 'Failed to access camera';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied. Please allow camera access and refresh the page.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found. Please connect a camera and try again.';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera is being used by another application. Please close other apps and try again.';
      }

      setError(errorMessage);
      setIsCameraOpen(false);
    }
  }, []);

  const startCamera = useCallback(async () => {
    try {
      setError(null);
      console.log('🎥 Starting camera...');
      console.log('🍎 User Agent:', navigator.userAgent);
      console.log('🌐 Platform:', navigator.platform);

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported in this browser. Please use Chrome, Firefox, or Safari.');
      }

      // Check permissions first
      try {
        const permissions = await navigator.permissions.query({ name: 'camera' as PermissionName });
        console.log('📋 Camera permission status:', permissions.state);

        if (permissions.state === 'denied') {
          throw new Error('Camera permission denied. Please enable camera access in browser settings.');
        }
      } catch (permError) {
        console.log('⚠️ Permission API not supported, continuing...');
      }

      // macOS specific constraints - start with basic then enhance
      let constraints: MediaStreamConstraints;

      if (navigator.userAgent.includes('Mac')) {
        console.log('🍎 Detected macOS - using optimized constraints');
        constraints = {
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user'
          },
          audio: false
        };
      } else {
        constraints = {
          video: {
            width: { ideal: 640, min: 320, max: 1280 },
            height: { ideal: 480, min: 240, max: 720 },
            facingMode: 'user',
            frameRate: { ideal: 30, min: 15 }
          },
          audio: false
        };
      }

      console.log('📋 Requesting camera with constraints:', constraints);
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ Camera stream obtained:', stream);
      console.log('📊 Stream tracks:', stream.getTracks().map(track => ({
        kind: track.kind,
        label: track.label,
        enabled: track.enabled,
        readyState: track.readyState
      })));

      if (videoRef.current) {
        // Clear any existing stream first
        if (videoRef.current.srcObject) {
          const oldStream = videoRef.current.srcObject as MediaStream;
          oldStream.getTracks().forEach(track => track.stop());
        }

        // CRITICAL: Set camera state BEFORE setting srcObject for macOS
        setIsCameraOpen(true);
        console.log('🎯 Camera state set to OPEN (BEFORE srcObject)');

        // Force a re-render to show UI
        setTimeout(() => setIsCameraOpen(true), 0);

        // Set new stream
        videoRef.current.srcObject = stream;
        console.log('📹 Stream assigned to video element');

        // Force video properties for better compatibility
        videoRef.current.muted = true;
        videoRef.current.playsInline = true;
        videoRef.current.autoplay = true;

        // Force immediate play for macOS
        videoRef.current.play().catch(console.error);

        // Handle video events
        videoRef.current.onloadedmetadata = () => {
          console.log('📹 Video metadata loaded:', {
            videoWidth: videoRef.current?.videoWidth,
            videoHeight: videoRef.current?.videoHeight,
            duration: videoRef.current?.duration
          });

          if (videoRef.current) {
            videoRef.current.play()
              .then(() => {
                console.log('▶️ Video playing successfully');
              })
              .catch((playError) => {
                console.error('❌ Error playing video:', playError);
                // Try to play again after a short delay
                setTimeout(() => {
                  if (videoRef.current) {
                    videoRef.current.play().catch(console.error);
                  }
                }, 500);
              });
          }
        };

        videoRef.current.oncanplay = () => {
          console.log('🎬 Video can play');
        };

        videoRef.current.onplaying = () => {
          console.log('🎥 Video is playing');
        };

        // Handle video loading errors
        videoRef.current.onerror = (videoError) => {
          console.error('❌ Video element error:', videoError);
          setError('Failed to load camera video');
        };

        // Multiple play attempts for better compatibility
        const playAttempts = [100, 500, 1000];
        playAttempts.forEach((delay, index) => {
          setTimeout(() => {
            if (videoRef.current && videoRef.current.paused) {
              console.log(`🔄 Play attempt ${index + 1} after ${delay}ms`);
              videoRef.current.play().catch(error => {
                console.log(`❌ Play attempt ${index + 1} failed:`, error);
              });
            }
          }, delay);
        });
      }
    } catch (error: any) {
      console.error('❌ Error accessing camera:', error);

      let errorMessage = 'Failed to access camera';

      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera access denied. Please allow camera permissions in browser settings and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found on this device.';
      } else if (error.name === 'NotReadableError') {
        errorMessage = 'Camera is already in use by another application. Please close other apps using camera.';
      } else if (error.name === 'OverconstrainedError') {
        errorMessage = 'Camera does not support the requested settings. Trying with basic settings...';

        // Retry with basic constraints
        try {
          const basicStream = await navigator.mediaDevices.getUserMedia({ video: true });
          if (videoRef.current) {
            videoRef.current.srcObject = basicStream;
            setIsCameraOpen(true);
            return;
          }
        } catch (retryError) {
          console.error('Retry with basic constraints failed:', retryError);
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      setIsCameraOpen(false);
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => {
        track.stop();
        console.log('Camera track stopped:', track.kind);
      });
      videoRef.current.srcObject = null;
      videoRef.current.onloadedmetadata = null;
      videoRef.current.onerror = null;
      setIsCameraOpen(false);
      console.log('Camera stopped successfully');
    }
  }, []);

  const capturePhoto = useCallback(() => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      const context = canvas.getContext('2d');

      // Debug camera state
      console.log('Camera capture attempt:', {
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        readyState: video.readyState,
        paused: video.paused,
        ended: video.ended
      });

      if (context && video.videoWidth > 0 && video.videoHeight > 0) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0);

        const imageDataUrl = canvas.toDataURL('image/jpeg');
        setCapturedImage(imageDataUrl);
        console.log('Photo captured successfully');

        // Automatically process the captured image for face recognition
        processImage(imageDataUrl);

        // Keep camera running for continuous capture
        // stopCamera(); // Commented out to keep camera running
      } else {
        console.error('Cannot capture photo: video not ready or invalid dimensions');
        setError('Camera not ready for capture. Please wait a moment and try again.');
      }
    } else {
      console.error('Video or canvas reference not available');
      setError('Camera or canvas not available for capture.');
    }
  }, []);

  const processImage = async (imageFile: File | string) => {
    setIsLoading(true);
    setResult(null);
    setError(null);

    try {
      // Validate and process image file
      if (typeof imageFile !== 'string' && imageFile instanceof File) {
        // Check file size (max 10MB)
        if (imageFile.size > 10 * 1024 * 1024) {
          throw new Error('Image file is too large. Maximum size is 10MB.');
        }

        // Check file type
        if (!imageFile.type.startsWith('image/')) {
          throw new Error('Please select a valid image file (JPG, PNG, or GIF).');
        }
      }

      const formData = new FormData();

      if (typeof imageFile === 'string') {
        try {
          // Convert data URL to blob
          const response = await fetch(imageFile);
          if (!response.ok) {
            throw new Error('Failed to process captured image');
          }
          const blob = await response.blob();
          formData.append('image', blob, 'captured-photo.jpg');
        } catch (error) {
          throw new Error('Failed to process captured image. Please try capturing again.');
        }
      } else {
        formData.append('image', imageFile);
      }

      const response = await fetch('/api/face-recognition', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data: RecognitionResult = await response.json();

      if (!data.success && data.error) {
        setResult({
          ...data,
          errorType: data.errorType || 'PROCESSING'
        });
      } else {
        setResult(data);
      }

    } catch (error: unknown) {
      console.error('Error processing image:', error);

      let errorMessage = 'Failed to process image';
      let errorType: RecognitionResult['errorType'] = 'UNKNOWN';

      if (error instanceof Error) {
        errorMessage = error.message;

        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorType = 'NETWORK';
        } else if (error.message.includes('face')) {
          errorType = 'NO_FACE';
        } else if (error.message.includes('reference')) {
          errorType = 'NO_REFERENCE';
        } else {
          errorType = 'PROCESSING';
        }
      }

      setResult({
        success: false,
        totalReferenceImages: 0,
        matches: [],
        matchCount: 0,
        error: errorMessage,
        errorType: errorType
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setError(null);

      // Validate file before processing
      if (file.size > 10 * 1024 * 1024) {
        setError('Image file is too large. Maximum size is 10MB.');
        return;
      }

      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file (JPG, PNG, GIF).');
        return;
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        const imageDataUrl = e.target?.result as string;
        setCapturedImage(imageDataUrl);
      };

      reader.onerror = () => {
        setError('Failed to read the selected file. Please try again.');
      };

      reader.readAsDataURL(file);
      processImage(file);
    }
  };

  const handleCapturedImageProcess = () => {
    if (capturedImage) {
      processImage(capturedImage);
    }
  };

  const resetAll = () => {
    setCapturedImage(null);
    setResult(null);
    setError(null);
    stopCamera();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Cleanup camera on component unmount
  useEffect(() => {
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <header className="text-center mb-8">
          {/* Professional Header with Icon */}
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-6 shadow-lg">
            <span className="text-3xl">👥</span>
          </div>

          <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Milan Face Recognition System
          </h1>

          <p className="text-gray-600 text-lg mb-6 max-w-2xl mx-auto">
            Advanced AI-powered face detection and matching system for events, security, and photo organization
          </p>

          {/* Feature Badges */}
          <div className="flex flex-wrap justify-center gap-3 mb-6">
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
              ✨ Group Photo Support (1-2 faces)
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium">
              🎯 50%+ Confidence Threshold
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 text-purple-800 text-sm font-medium">
              🚀 Real-time Processing
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-orange-100 text-orange-800 text-sm font-medium">
              🔒 Local Processing
            </span>
          </div>

          {/* System Status */}
          <div className="inline-flex items-center bg-green-50 border border-green-200 rounded-full px-6 py-3 shadow-sm">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></div>
            <span className="text-green-700 font-medium">
              System Ready • Reference Database Available
            </span>
          </div>
        </header>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setError(null)}
                  className="inline-flex text-red-400 hover:text-red-600"
                >
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Camera Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">Camera Capture</h2>

              {/* Enhanced Debug info */}
              <div className="text-xs text-gray-600 bg-gray-100 p-3 rounded-lg border">
                <div className="grid grid-cols-2 gap-2">
                  <div>Camera State: <span className={isCameraOpen ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>{isCameraOpen ? 'OPEN' : 'CLOSED'}</span></div>
                  <div>Captured: <span className={capturedImage ? 'text-green-600 font-bold' : 'text-gray-500'}>{capturedImage ? 'YES' : 'NO'}</span></div>
                  <div>Video Stream: <span className={videoRef.current?.srcObject ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>{videoRef.current?.srcObject ? 'ACTIVE' : 'NONE'}</span></div>
                  <div>Video Playing: <span className={videoRef.current?.paused === false ? 'text-green-600 font-bold' : 'text-red-600 font-bold'}>{videoRef.current?.paused === false ? 'YES' : 'NO'}</span></div>
                </div>
              </div>

              {!isCameraOpen && !capturedImage && (
                <div className="space-y-3">
                  <button
                    onClick={startCameraSimple}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors text-lg font-bold"
                  >
                    ✨ Simple Camera Start (Reference Code)
                  </button>

                  <button
                    onClick={startCamera}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    🎥 Start Camera (Advanced)
                  </button>

                  {/* Force Camera Button - macOS Emergency Fix */}
                  <button
                    onClick={async () => {
                      try {
                        console.log('🚨 FORCE CAMERA START - macOS Emergency Fix');

                        // Get stream directly
                        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                        console.log('✅ Emergency stream obtained:', stream);

                        if (videoRef.current) {
                          videoRef.current.srcObject = stream;
                          videoRef.current.muted = true;
                          videoRef.current.playsInline = true;

                          // Force UI state
                          setIsCameraOpen(true);
                          setError(null);

                          // Force play
                          setTimeout(() => {
                            if (videoRef.current) {
                              videoRef.current.play().then(() => {
                                console.log('🎥 Emergency camera started successfully!');
                                alert('✅ Emergency camera fix applied! Camera should now be visible.');
                              }).catch(console.error);
                            }
                          }, 100);
                        }
                      } catch (error) {
                        console.error('Emergency camera failed:', error);
                        alert('❌ Emergency fix failed: ' + error);
                      }
                    }}
                    className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors text-lg font-bold"
                  >
                    🚨 FORCE START CAMERA (Emergency)
                  </button>

                  {/* Diagnostic Button */}
                  <button
                    onClick={async () => {
                      try {
                        console.log('🔍 Running camera diagnostics...');

                        // Check basic support
                        const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
                        console.log('📱 getUserMedia support:', hasGetUserMedia);

                        // Check permissions
                        let permissionStatus = 'unknown';
                        try {
                          const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
                          permissionStatus = permission.state;
                          console.log('🔐 Permission status:', permissionStatus);
                        } catch (e) {
                          console.log('⚠️ Permission API not available');
                        }

                        // Try to enumerate devices
                        let deviceCount = 0;
                        try {
                          const devices = await navigator.mediaDevices.enumerateDevices();
                          const videoDevices = devices.filter(device => device.kind === 'videoinput');
                          deviceCount = videoDevices.length;
                          console.log('📹 Video devices found:', deviceCount);
                          console.log('📋 Devices:', videoDevices.map(d => ({ label: d.label, deviceId: d.deviceId })));
                        } catch (e) {
                          console.log('❌ Cannot enumerate devices');
                        }

                        // Show results
                        alert(`🔍 Camera Diagnostics:

✅ getUserMedia Support: ${hasGetUserMedia ? 'YES' : 'NO'}
🔐 Permission Status: ${permissionStatus}
📹 Video Devices Found: ${deviceCount}
🍎 macOS Detected: ${navigator.userAgent.includes('Mac') ? 'YES' : 'NO'}
🌐 Browser: ${navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Safari') ? 'Safari' : 'Other'}

${permissionStatus === 'denied' ? '❌ ISSUE: Camera permission denied!' : ''}
${!hasGetUserMedia ? '❌ ISSUE: Browser not supported!' : ''}
${deviceCount === 0 ? '❌ ISSUE: No camera devices found!' : ''}

📋 Next Steps:
1. System Preferences > Security & Privacy > Camera
2. Enable browser access
3. Refresh page and try again`);

                      } catch (error) {
                        console.error('Diagnostic error:', error);
                        alert('Diagnostic failed: ' + error);
                      }
                    }}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                  >
                    🔍 Run Camera Diagnostics
                  </button>

                  {/* macOS Help Button */}
                  <button
                    onClick={() => {
                      alert(`🍎 macOS Camera Setup Guide:

1️⃣ System Settings:
   • Apple Menu > System Preferences
   • Security & Privacy > Privacy > Camera
   • Enable Chrome/Browser access ✅

2️⃣ Chrome Settings:
   • Go to: chrome://settings/content/camera
   • Select "Ask before accessing"
   • Check if localhost:3002 is blocked

3️⃣ Site Permissions:
   • Click lock icon in address bar
   • Set Camera to "Allow"
   • Refresh page

4️⃣ Troubleshooting:
   • Close other apps using camera
   • Restart Chrome
   • Check System Preferences again

Current: ${navigator.userAgent.includes('Mac') ? 'macOS ✅' : 'Other OS ❌'}`);
                    }}
                    className="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                  >
                    🍎 macOS Setup Guide
                  </button>
                </div>
              )}

              {/* Force show camera controls - macOS fix */}
              {(isCameraOpen || videoRef.current?.srcObject || (videoRef.current && videoRef.current.srcObject !== null)) && (
                <div className="space-y-4">
                  <div className="relative bg-gray-900 rounded-lg overflow-hidden border-4 border-blue-400 shadow-2xl">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      controls={false}
                      className="w-full h-auto rounded-lg bg-gray-900 display-block"
                      style={{
                        minHeight: '300px',
                        maxHeight: '500px',
                        objectFit: 'cover',
                        transform: 'scaleX(-1)', // Mirror effect for selfie camera
                        display: 'block',
                        visibility: 'visible'
                      }}
                      onLoadedMetadata={() => {
                        console.log('🎬 Video metadata loaded in UI');
                      }}
                      onCanPlay={() => {
                        console.log('🎥 Video can play in UI');
                      }}
                      onPlay={() => {
                        console.log('▶️ Video started playing in UI');
                      }}
                    />

                    {/* Live indicator */}
                    <div className="absolute top-4 left-4 flex items-center bg-red-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg z-10">
                      <div className="w-3 h-3 bg-white rounded-full mr-2 animate-pulse"></div>
                      🔴 LIVE
                    </div>

                    {/* Camera status indicator */}
                    <div className="absolute bottom-4 left-4 bg-green-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg z-10">
                      📹 Camera Active
                    </div>

                    {/* Video dimensions display */}
                    <div className="absolute top-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-full text-xs font-medium shadow-lg z-10">
                      📐 Video Ready
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-blue-800 text-sm">
                      📸 Camera is ready! Click Capture & Match to take photo and automatically find matches.
                    </p>
                  </div>

                  {/* Always show capture button when camera is active */}
                  <div className="grid grid-cols-1 gap-2">
                    <button
                      onClick={capturePhoto}
                      disabled={isLoading}
                      className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-lg"
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>
                          📸 Capture & Match Face
                        </>
                      )}
                    </button>

                    <div className="flex gap-2">
                      <button
                        onClick={() => {
                          setCapturedImage(null);
                          setResult(null);
                          setError(null);
                        }}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Clear Results
                      </button>
                      <button
                        onClick={stopCamera}
                        className="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Stop Camera
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {capturedImage && !isCameraOpen && (
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={capturedImage}
                      alt="Captured"
                      className="w-full rounded-lg border"
                    />
                    <div className="absolute top-3 left-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      📸 Captured
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={handleCapturedImageProcess}
                      disabled={isLoading}
                      className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      {isLoading ? 'Processing...' : 'Recognize Face'}
                    </button>
                    <button
                      onClick={resetAll}
                      className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                      Reset
                    </button>
                  </div>
                </div>
              )}

              {/* Emergency fallback - always show capture if video stream exists */}
              {!isCameraOpen && videoRef.current?.srcObject && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-yellow-800 text-sm mb-3">
                    🚨 Camera detected but UI not showing. Emergency controls:
                  </p>
                  <button
                    onClick={() => {
                      console.log('Emergency camera state fix');
                      setIsCameraOpen(true);
                    }}
                    className="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors mb-2"
                  >
                    🔧 Fix Camera UI
                  </button>
                  <button
                    onClick={capturePhoto}
                    disabled={isLoading}
                    className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                  >
                    📸 Emergency Capture
                  </button>
                </div>
              )}

              <canvas ref={canvasRef} className="hidden" />
            </div>

            {/* File Upload Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800">File Upload</h2>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="cursor-pointer block"
                >
                  <div className="text-gray-600 mb-2">
                    <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-600">
                    Click to upload an image
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    JPG, PNG, GIF up to 10MB
                  </p>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Loading Overlay */}
        {isLoading && (
          <div className="bg-white rounded-xl shadow-2xl p-12 border border-gray-100 text-center">
            <div className="relative mb-8">
              <div className="inline-block animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-blue-600"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-3xl">🔍</span>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-800">Processing Your Image</h3>
              <p className="text-gray-600 text-lg">Our AI is analyzing faces and matching against the reference database...</p>
              <div className="flex justify-center space-x-2 mt-6">
                <div className="w-3 h-3 bg-blue-600 rounded-full animate-bounce"></div>
                <div className="w-3 h-3 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                <div className="w-3 h-3 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
              </div>
              <div className="mt-8 bg-blue-50 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                  <strong>Processing Steps:</strong> Face Detection → Feature Extraction → Database Matching → Results Generation
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Results Section */}
        {result && (
          <div className="bg-white rounded-xl shadow-2xl p-8 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Recognition Results</h2>

            {result.error ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {result.errorType === 'NO_FACE' && 'No Face Detected'}
                      {result.errorType === 'NO_REFERENCE' && 'No Reference Images'}
                      {result.errorType === 'NETWORK' && 'Network Error'}
                      {result.errorType === 'PROCESSING' && 'Processing Error'}
                      {(!result.errorType || result.errorType === 'UNKNOWN') && 'Recognition Failed'}
                    </h3>
                    <p className="text-sm text-red-700 mt-1">{result.error}</p>
                    {result.errorType === 'NO_FACE' && (
                      <p className="text-xs text-red-600 mt-2">
                        💡 Try: Ensure faces are clearly visible, well-lit, and looking towards the camera. Works with single faces or group photos.
                      </p>
                    )}
                    {result.errorType === 'TOO_MANY_FACES' && (
                      <p className="text-xs text-red-600 mt-2">
                        💡 Try: Use photos with only 1 or 2 people. For larger groups, crop the image to focus on 1-2 faces you want to match.
                      </p>
                    )}
                    {result.errorType === 'NO_REFERENCE' && (
                      <p className="text-xs text-red-600 mt-2">
                        💡 Try: Add reference images to the public/reference-images folder.
                      </p>
                    )}
                    {result.errorType === 'NETWORK' && (
                      <p className="text-xs text-red-600 mt-2">
                        💡 Try: Check your internet connection and try again.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Summary Statistics */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-blue-600">{result.totalReferenceImages}</p>
                      <p className="text-sm text-blue-800">Reference Images</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-orange-600">{result.detectedFaceCount || 1}</p>
                      <p className="text-sm text-orange-800">Faces Detected</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-green-600">{result.matchCount}</p>
                      <p className="text-sm text-green-800">Total Matches</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-purple-600">
                        {result.matchCount > 0 ? Math.round((result.matchCount / result.totalReferenceImages) * 100) : 0}%
                      </p>
                      <p className="text-sm text-purple-800">Match Rate</p>
                    </div>
                  </div>
                </div>

                {/* Detected Faces Analysis */}
                {result.detectedFaces && result.detectedFaces.length > 0 && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">
                      Detected Faces Analysis ({result.detectedFaces.length} face{result.detectedFaces.length !== 1 ? 's' : ''})
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {result.detectedFaces.map((face, index) => (
                        <div key={index} className="bg-white border border-gray-300 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-800">Face {face.faceIndex + 1}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              face.matchCount > 0
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {face.matchCount} match{face.matchCount !== 1 ? 'es' : ''}
                            </span>
                          </div>
                          <div className="text-xs text-gray-600 mb-2">
                            Position: ({face.boundingBox.x}, {face.boundingBox.y})
                            Size: {face.boundingBox.width}×{face.boundingBox.height}
                          </div>
                          {face.bestMatch && (
                            <div className="bg-green-50 border border-green-200 rounded p-2">
                              <p className="text-xs font-medium text-green-800">Best Match:</p>
                              <p className="text-xs text-green-700">{face.bestMatch.filename}</p>
                              <p className="text-xs text-green-600">{face.bestMatch.confidence}% confidence</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Query Image with Face Bounding Boxes */}
                {capturedImage && result.detectedFaces && result.detectedFaces.length > 0 && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">Query Image with Detected Faces</h3>
                    <div className="flex justify-center">
                      <div className="relative inline-block">
                        <img
                          src={capturedImage}
                          alt="Query with face detection"
                          className="max-w-full max-h-96 object-contain rounded-lg border-2 border-blue-300"
                          style={{ maxWidth: '600px' }}
                        />
                        {/* Face bounding boxes overlay */}
                        <svg
                          className="absolute top-0 left-0 w-full h-full pointer-events-none"
                          viewBox="0 0 100 100"
                          preserveAspectRatio="none"
                        >
                          {result.detectedFaces.map((face, index) => {
                            // Convert absolute coordinates to percentage for responsive overlay
                            const img = document.querySelector('img[alt="Query with face detection"]') as HTMLImageElement;
                            if (!img) return null;

                            return (
                              <g key={index}>
                                <rect
                                  x={`${(face.boundingBox.x / img.naturalWidth) * 100}%`}
                                  y={`${(face.boundingBox.y / img.naturalHeight) * 100}%`}
                                  width={`${(face.boundingBox.width / img.naturalWidth) * 100}%`}
                                  height={`${(face.boundingBox.height / img.naturalHeight) * 100}%`}
                                  fill="none"
                                  stroke={face.matchCount > 0 ? "#10B981" : "#EF4444"}
                                  strokeWidth="0.5"
                                  className="animate-pulse"
                                />
                                <text
                                  x={`${(face.boundingBox.x / img.naturalWidth) * 100}%`}
                                  y={`${((face.boundingBox.y - 5) / img.naturalHeight) * 100}%`}
                                  fill={face.matchCount > 0 ? "#10B981" : "#EF4444"}
                                  fontSize="2"
                                  fontWeight="bold"
                                >
                                  Face {face.faceIndex + 1}
                                </text>
                              </g>
                            );
                          })}
                        </svg>
                        <div className="absolute -top-2 -right-2 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          {result.detectedFaces.length} Face{result.detectedFaces.length !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Matched Images Display */}
                {result.matches.length > 0 ? (
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-xl font-semibold text-gray-800">
                        Matched Reference Images ({result.matchCount} found)
                      </h3>
                      <div className="text-sm text-gray-600">
                        Best Match: {result.matches[0]?.confidence}% | Min: {result.confidenceThreshold || 50}%
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {result.matches.map((match, index) => (
                        <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                          <div className="aspect-square relative">
                            <img
                              src={`/reference-images/${match.filename}`}
                              alt={match.filename}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute top-2 right-2">
                              <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                {match.confidence}%
                              </span>
                            </div>
                            {/* Face indicator for group photos */}
                            {(result.detectedFaceCount || 1) > 1 && (
                              <div className="absolute top-2 left-2">
                                <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                  Face {match.faceIndex + 1}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="p-3">
                            <p className="font-medium text-gray-800 text-sm truncate" title={match.filename}>
                              {match.filename}
                            </p>
                            <div className="flex justify-between items-center mt-2">
                              <p className="text-green-600 font-semibold text-sm">
                                {match.confidence}% Match
                              </p>
                              <p className="text-xs text-gray-500">
                                D: {match.distance.toFixed(3)}
                              </p>
                            </div>
                            {/* Additional info for group photos */}
                            <div className="mt-1 space-y-1">
                              {(result.detectedFaceCount || 1) > 1 && (
                                <p className="text-xs text-blue-600">
                                  Query: Face {match.faceIndex + 1} at ({match.boundingBox.x}, {match.boundingBox.y})
                                </p>
                              )}
                              {(match.referenceTotalFaces || 1) > 1 && (
                                <p className="text-xs text-green-600">
                                  Reference: Face {(match.referenceFaceIndex || 0) + 1}/{match.referenceTotalFaces} in {match.filename}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                    <div className="text-yellow-600 mb-2">
                      <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728" />
                      </svg>
                    </div>
                    <p className="text-yellow-800 font-medium">No matches found above the confidence threshold</p>
                    <p className="text-yellow-600 text-sm mt-1">Try with a clearer image or adjust lighting conditions</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
