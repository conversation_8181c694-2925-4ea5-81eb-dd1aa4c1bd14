'use client';

import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold text-gray-800 mb-4">
              🎯 Milan Face Recognition
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Advanced AI-powered face recognition system with live camera capture
            </p>
          </div>

          {/* Main Features Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Live Camera Feature */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-center">
                <div className="text-6xl mb-4">📹</div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Live Camera</h2>
                <p className="text-gray-600 mb-6">
                  Capture photos in real-time using your camera and instantly find matching faces from our database.
                </p>
                <Link
                  href="/camera"
                  className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                >
                  🎥 Start Camera
                </Link>
              </div>
            </div>

            {/* File Upload Feature */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-center">
                <div className="text-6xl mb-4">📁</div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Upload Image</h2>
                <p className="text-gray-600 mb-6">
                  Upload an image file from your device and let our AI find matching faces in the reference database.
                </p>
                <Link
                  href="/upload"
                  className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                >
                  📤 Upload Image
                </Link>
              </div>
            </div>
          </div>

          {/* Features List */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">✨ Key Features</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">🎯</div>
                <div>
                  <h4 className="font-semibold text-gray-800">High Accuracy</h4>
                  <p className="text-gray-600 text-sm">Advanced AI algorithms for precise face matching</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="text-2xl">⚡</div>
                <div>
                  <h4 className="font-semibold text-gray-800">Real-time Processing</h4>
                  <p className="text-gray-600 text-sm">Instant results with optimized performance</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="text-2xl">🔒</div>
                <div>
                  <h4 className="font-semibold text-gray-800">Secure & Private</h4>
                  <p className="text-gray-600 text-sm">Your images are processed securely</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="text-2xl">📱</div>
                <div>
                  <h4 className="font-semibold text-gray-800">Multi-device Support</h4>
                  <p className="text-gray-600 text-sm">Works on desktop, tablet, and mobile</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="text-2xl">🎨</div>
                <div>
                  <h4 className="font-semibold text-gray-800">Visual Results</h4>
                  <p className="text-gray-600 text-sm">Clear visualization of detected faces and matches</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="text-2xl">📊</div>
                <div>
                  <h4 className="font-semibold text-gray-800">Confidence Scores</h4>
                  <p className="text-gray-600 text-sm">Detailed confidence ratings for each match</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Start */}
          <div className="text-center mt-12">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">🚀 Quick Start</h3>
            <p className="text-gray-600 mb-6">
              Choose your preferred method to get started with face recognition
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/camera"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors"
              >
                📹 Use Camera
              </Link>
              <Link
                href="/upload"
                className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-lg transition-colors"
              >
                📁 Upload File
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}