'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';

interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface DetectedFace {
  bbox: BoundingBox;
  confidence: number;
}

interface FaceMatch {
  filename: string;
  confidence: number;
  bbox: BoundingBox;
}

interface FaceRecognitionResult {
  query_faces: DetectedFace[];
  matches: FaceMatch[];
  total_matches: number;
  processing_time: number;
}

export default function UploadPage() {
  const [result, setResult] = useState<FaceRecognitionResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size must be less than 10MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setUploadedImage(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Process face recognition
    await processFaceRecognition(file);
  };

  const processFaceRecognition = async (file: File) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('image', file);

      console.log('🔍 Sending image for face recognition...');
      
      const apiResponse = await fetch('/api/face-recognition', {
        method: 'POST',
        body: formData,
      });

      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status}`);
      }

      const data: FaceRecognitionResult = await apiResponse.json();
      console.log('✅ Face recognition result:', data);
      
      setResult(data);
    } catch (error) {
      console.error('Face recognition error:', error);
      setError('Face recognition failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const resetUpload = () => {
    setUploadedImage(null);
    setResult(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <Link 
            href="/"
            className="inline-block mb-4 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back to Home
          </Link>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            📁 Upload Image for Face Recognition
          </h1>
          <p className="text-gray-600">
            Upload an image file and let our AI find matching faces
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">❌ {error}</p>
            </div>
          )}

          {/* Upload Area */}
          {!uploadedImage && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-gray-400 transition-colors">
              <div className="text-6xl mb-4">📤</div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">Upload Your Image</h3>
              <p className="text-gray-500 mb-6">
                Drag and drop an image file here, or click to select
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {isLoading ? 'Processing...' : 'Select Image'}
              </button>
              <p className="text-sm text-gray-400 mt-4">
                Supported formats: JPG, PNG, GIF (Max 10MB)
              </p>
            </div>
          )}

          {/* Uploaded Image */}
          {uploadedImage && (
            <div className="space-y-4 mb-6">
              <div className="relative">
                <img
                  src={uploadedImage}
                  alt="Uploaded"
                  className="w-full max-h-96 object-contain rounded-lg border"
                />
                <div className="absolute top-3 left-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  📁 Uploaded
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={resetUpload}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  📤 Upload New Image
                </button>
                <button
                  onClick={() => {
                    if (fileInputRef.current?.files?.[0]) {
                      processFaceRecognition(fileInputRef.current.files[0]);
                    }
                  }}
                  disabled={isLoading}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  {isLoading ? 'Processing...' : '🔍 Re-analyze'}
                </button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Processing your image...</p>
            </div>
          )}

          {/* Face Recognition Results */}
          {result && (
            <div className="bg-white rounded-xl shadow-lg p-6 mt-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">🎯 Face Recognition Results</h2>

              {/* Processing Info */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-green-800">Faces Detected:</span>
                    <div className="text-green-600">{result.query_faces.length}</div>
                  </div>
                  <div>
                    <span className="font-medium text-green-800">Total Matches:</span>
                    <div className="text-green-600">{result.total_matches}</div>
                  </div>
                  <div>
                    <span className="font-medium text-green-800">Processing Time:</span>
                    <div className="text-green-600">{result.processing_time.toFixed(2)}s</div>
                  </div>
                  <div>
                    <span className="font-medium text-green-800">Status:</span>
                    <div className="text-green-600">✅ Complete</div>
                  </div>
                </div>
              </div>

              {/* Query Image with Face Detection */}
              {uploadedImage && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Query Image with Detected Faces</h3>
                  <div className="flex justify-center">
                    <div className="relative inline-block">
                      <img
                        src={uploadedImage}
                        alt="Query with face detection"
                        className="max-w-full max-h-96 object-contain rounded-lg border-2 border-green-300"
                        style={{ maxWidth: '600px' }}
                      />
                      {/* Face bounding boxes overlay */}
                      <svg
                        className="absolute top-0 left-0 w-full h-full pointer-events-none"
                        viewBox="0 0 100 100"
                        preserveAspectRatio="none"
                      >
                        {result.query_faces.map((face, index) => (
                          <rect
                            key={index}
                            x={face.bbox.x}
                            y={face.bbox.y}
                            width={face.bbox.width}
                            height={face.bbox.height}
                            fill="none"
                            stroke="#10b981"
                            strokeWidth="0.5"
                            vectorEffect="non-scaling-stroke"
                          />
                        ))}
                      </svg>
                    </div>
                  </div>
                </div>
              )}

              {/* Matches */}
              {result.matches.length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    🎯 Found Matches ({result.matches.length})
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {result.matches.map((match, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                        <div className="aspect-square relative">
                          <img
                            src={`/reference-images/${match.filename}`}
                            alt={match.filename}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 right-2">
                            <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {match.confidence}%
                            </span>
                          </div>
                        </div>
                        <div className="p-3">
                          <p className="text-sm font-medium text-gray-800 truncate">
                            {match.filename.replace(/\.[^/.]+$/, "")}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Confidence: {match.confidence}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">😔</div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">No Matches Found</h3>
                  <p className="text-gray-500">
                    No faces in the uploaded image matched our reference database.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
