"use client";

import { useEffect, useRef, useState } from "react";

export default function CameraTest() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [streamInfo, setStreamInfo] = useState<any>(null);

  // Simple camera function based on your reference code
  async function getCameraStream() {
    try {
      setError(null);
      console.log('🎥 Starting simple camera...');
      
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      console.log('✅ Stream obtained:', stream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsCameraActive(true);
        
        // Set stream info for debugging
        setStreamInfo({
          id: stream.id,
          active: stream.active,
          tracks: stream.getTracks().map(track => ({
            kind: track.kind,
            label: track.label,
            enabled: track.enabled,
            readyState: track.readyState
          }))
        });
      }
    } catch (error: any) {
      console.error("Camera access denied or error: ", error);
      setError(`Camera Error: ${error.name} - ${error.message}`);
      setIsCameraActive(false);
    }
  }

  // Stop camera function
  function stopCamera() {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsCameraActive(false);
      setStreamInfo(null);
      console.log('🛑 Camera stopped');
    }
  }

  // Auto-start camera on component mount (optional)
  useEffect(() => {
    // Uncomment next line to auto-start camera
    // getCameraStream();

    // Cleanup on unmount
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
            📹 Camera Test Page
          </h1>
          
          <div className="text-center mb-6">
            <p className="text-gray-600 mb-4">
              Simple camera test using your reference code approach
            </p>
            
            {/* Control Buttons */}
            <div className="space-x-4">
              <button
                onClick={getCameraStream}
                disabled={isCameraActive}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  isCameraActive 
                    ? 'bg-gray-400 cursor-not-allowed text-white' 
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isCameraActive ? '✅ Camera Active' : '🎥 Start Camera'}
              </button>
              
              <button
                onClick={stopCamera}
                disabled={!isCameraActive}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  !isCameraActive 
                    ? 'bg-gray-400 cursor-not-allowed text-white' 
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                🛑 Stop Camera
              </button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Camera Status */}
          <div className="bg-gray-100 p-4 rounded-lg mb-6">
            <h3 className="font-semibold mb-2">📊 Camera Status:</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Status:</span> 
                <span className={`ml-2 font-bold ${isCameraActive ? 'text-green-600' : 'text-red-600'}`}>
                  {isCameraActive ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </div>
              <div>
                <span className="font-medium">Video Element:</span> 
                <span className={`ml-2 font-bold ${videoRef.current?.srcObject ? 'text-green-600' : 'text-red-600'}`}>
                  {videoRef.current?.srcObject ? 'HAS STREAM' : 'NO STREAM'}
                </span>
              </div>
              <div>
                <span className="font-medium">Playing:</span> 
                <span className={`ml-2 font-bold ${videoRef.current?.paused === false ? 'text-green-600' : 'text-red-600'}`}>
                  {videoRef.current?.paused === false ? 'YES' : 'NO'}
                </span>
              </div>
              <div>
                <span className="font-medium">Browser:</span> 
                <span className="ml-2 font-bold text-blue-600">
                  {navigator.userAgent.includes('Mac') ? 'macOS' : 'Other'} - 
                  {navigator.userAgent.includes('Chrome') ? 'Chrome' : 
                   navigator.userAgent.includes('Safari') ? 'Safari' : 'Other'}
                </span>
              </div>
            </div>
          </div>

          {/* Video Display */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="rounded-lg border-4 border-blue-300 shadow-lg"
                style={{
                  width: '100%',
                  maxWidth: '640px',
                  height: 'auto',
                  minHeight: '240px',
                  backgroundColor: '#000'
                }}
                onLoadedMetadata={() => {
                  console.log('📹 Video metadata loaded');
                }}
                onCanPlay={() => {
                  console.log('▶️ Video can play');
                }}
                onPlay={() => {
                  console.log('🎥 Video started playing');
                }}
                onError={(e) => {
                  console.error('❌ Video error:', e);
                }}
              />
              
              {/* Live indicator */}
              {isCameraActive && (
                <div className="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  <div className="inline-block w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                  🔴 LIVE
                </div>
              )}
            </div>
          </div>

          {/* Stream Info Debug */}
          {streamInfo && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">🔍 Stream Debug Info:</h3>
              <pre className="text-xs bg-white p-3 rounded border overflow-auto">
                {JSON.stringify(streamInfo, null, 2)}
              </pre>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">📋 Instructions:</h3>
            <ol className="list-decimal list-inside text-sm space-y-1">
              <li>Click "🎥 Start Camera" button</li>
              <li>Allow camera permission when browser asks</li>
              <li>Camera feed should appear in the video box above</li>
              <li>Check the status indicators turn GREEN</li>
              <li>Check browser console for detailed logs</li>
            </ol>
          </div>

          {/* Back to Main App */}
          <div className="text-center mt-6">
            <a 
              href="/" 
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              ← Back to Face Recognition App
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
