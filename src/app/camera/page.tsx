'use client';

import { useState, useRef, useCallback, useEffect } from 'react';

interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface DetectedFace {
  bbox: BoundingBox;
  confidence: number;
}

interface FaceMatch {
  filename: string;
  confidence: number;
  bbox: BoundingBox;
}

interface FaceRecognitionResult {
  query_faces: DetectedFace[];
  matches: FaceMatch[];
  total_matches: number;
  processing_time: number;
}

export default function CameraPage() {
  const [result, setResult] = useState<FaceRecognitionResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Start Camera
  const startCamera = useCallback(async () => {
    try {
      setError(null);
      console.log('🎥 Starting camera...');

      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      console.log('✅ Camera stream obtained');

      if (videoRef.current) {
        // Clean up existing stream
        if (videoRef.current.srcObject) {
          const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
          tracks.forEach((track: MediaStreamTrack) => track.stop());
        }

        // Set stream to video element
        videoRef.current.srcObject = stream;
        videoRef.current.muted = true;
        videoRef.current.playsInline = true;
        videoRef.current.autoplay = true;

        setIsCameraOpen(true);
        console.log('🎯 Camera started successfully');
        
        // Force play the video
        setTimeout(() => {
          if (videoRef.current) {
            videoRef.current.play().catch(console.error);
          }
        }, 100);
      }
    } catch (error: any) {
      console.error("Camera error:", error);
      let errorMessage = 'Failed to access camera';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied. Please allow camera access.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No camera found.';
      }
      setError(errorMessage);
      setIsCameraOpen(false);
    }
  }, []);

  // Stop Camera
  const stopCamera = useCallback(() => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsCameraOpen(false);
      console.log('Camera stopped');
    }
  }, []);

  // Capture Photo and Process
  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const context = canvas.getContext('2d');

    if (context && video.videoWidth > 0 && video.videoHeight > 0) {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0);
      
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);
      setCapturedImage(imageDataUrl);
      
      // Stop camera after capture
      stopCamera();
      
      // Process face recognition
      await processFaceRecognition(imageDataUrl);
    }
  }, [stopCamera]);

  // Process Face Recognition
  const processFaceRecognition = async (imageDataUrl: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      
      // Convert data URL to blob
      const response = await fetch(imageDataUrl);
      const blob = await response.blob();
      formData.append('image', blob, 'captured-photo.jpg');

      console.log('🔍 Sending image for face recognition...');
      
      const apiResponse = await fetch('/api/face-recognition', {
        method: 'POST',
        body: formData,
      });

      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status}`);
      }

      const data: FaceRecognitionResult = await apiResponse.json();
      console.log('✅ Face recognition result:', data);
      
      setResult(data);
    } catch (error) {
      console.error('Face recognition error:', error);
      setError('Face recognition failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-8">
            🎥 Face Recognition Camera
          </h1>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <p className="text-red-800">❌ {error}</p>
            </div>
          )}

          {/* Start Camera Button */}
          {!isCameraOpen && !capturedImage && (
            <div className="text-center mb-6">
              <button
                onClick={startCamera}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors text-lg"
              >
                🎥 Start Camera
              </button>
            </div>
          )}

          {/* Camera View */}
          {isCameraOpen && (
            <div className="space-y-4 mb-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-green-800 text-sm">
                  📹 Camera is active! Click capture to take photo and find matches.
                </p>
              </div>
              
              <div className="relative bg-gray-900 rounded-lg overflow-hidden border-2 border-blue-400">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-80 object-cover"
                  style={{ minHeight: '320px', backgroundColor: '#000' }}
                />
                <canvas
                  ref={canvasRef}
                  className="absolute top-0 left-0 w-full h-full pointer-events-none"
                />
                
                {/* Live indicator */}
                <div className="absolute top-4 left-4 flex items-center bg-red-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg">
                  <div className="w-3 h-3 bg-white rounded-full mr-2 animate-pulse"></div>
                  🔴 LIVE
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={capturePhoto}
                  disabled={isLoading}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors text-lg"
                >
                  {isLoading ? 'Processing...' : '📸 Capture & Match Face'}
                </button>
                <button
                  onClick={stopCamera}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  ⏹️ Stop Camera
                </button>
              </div>
            </div>
          )}

          {/* Captured Image */}
          {capturedImage && !isCameraOpen && (
            <div className="space-y-4 mb-6">
              <div className="relative">
                <img
                  src={capturedImage}
                  alt="Captured"
                  className="w-full rounded-lg border"
                />
                <div className="absolute top-3 left-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  📸 Captured
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setCapturedImage(null);
                    setResult(null);
                    setError(null);
                  }}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  📷 Take New Photo
                </button>
                <button
                  onClick={() => processFaceRecognition(capturedImage)}
                  disabled={isLoading}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  {isLoading ? 'Processing...' : '🔍 Re-analyze'}
                </button>
              </div>
            </div>
          )}

          {/* Face Recognition Results */}
          {result && (
            <div className="bg-white rounded-xl shadow-lg p-6 mt-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">🎯 Face Recognition Results</h2>

              {/* Processing Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Faces Detected:</span>
                    <div className="text-blue-600">{result.query_faces.length}</div>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Total Matches:</span>
                    <div className="text-blue-600">{result.total_matches}</div>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Processing Time:</span>
                    <div className="text-blue-600">{result.processing_time.toFixed(2)}s</div>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Status:</span>
                    <div className="text-green-600">✅ Complete</div>
                  </div>
                </div>
              </div>

              {/* Query Image with Face Detection */}
              {capturedImage && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">Query Image with Detected Faces</h3>
                  <div className="flex justify-center">
                    <div className="relative inline-block">
                      <img
                        src={capturedImage}
                        alt="Query with face detection"
                        className="max-w-full max-h-96 object-contain rounded-lg border-2 border-blue-300"
                        style={{ maxWidth: '600px' }}
                      />
                      {/* Face bounding boxes overlay */}
                      <svg
                        className="absolute top-0 left-0 w-full h-full pointer-events-none"
                        viewBox="0 0 100 100"
                        preserveAspectRatio="none"
                      >
                        {result.query_faces.map((face, index) => (
                          <rect
                            key={index}
                            x={face.bbox.x}
                            y={face.bbox.y}
                            width={face.bbox.width}
                            height={face.bbox.height}
                            fill="none"
                            stroke="#ef4444"
                            strokeWidth="0.5"
                            vectorEffect="non-scaling-stroke"
                          />
                        ))}
                      </svg>
                    </div>
                  </div>
                </div>
              )}

              {/* Matches */}
              {result.matches.length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    🎯 Found Matches ({result.matches.length})
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {result.matches.map((match, index) => (
                      <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden">
                        <div className="aspect-square relative">
                          <img
                            src={`/reference-images/${match.filename}`}
                            alt={match.filename}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 right-2">
                            <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {match.confidence}%
                            </span>
                          </div>
                        </div>
                        <div className="p-3">
                          <p className="text-sm font-medium text-gray-800 truncate">
                            {match.filename.replace(/\.[^/.]+$/, "")}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Confidence: {match.confidence}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">😔</div>
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">No Matches Found</h3>
                  <p className="text-gray-500">
                    No faces in the captured image matched our reference database.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
